{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\CartPopup.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport { useQuery, useMutation } from '@apollo/client';\nimport { GET_CART_QUERY } from '../graphql/queries';\nimport { UPDATE_CART_MUTATION } from '../graphql/mutations';\nimport { useToast } from '../contexts/ToastContext';\nimport '../styles/CartPopup.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction CartPopup({\n  isOpen,\n  closePopup,\n  cartItems\n}) {\n  _s();\n  const {\n    loading,\n    error,\n    data,\n    refetch\n  } = useQuery(GET_CART_QUERY, {\n    fetchPolicy: 'cache-and-network',\n    notifyOnNetworkStatusChange: true\n  });\n\n  // State for managing selected options for each cart item\n  const [selectedOptions] = useState({});\n  const {\n    showSuccess,\n    showError\n  } = useToast();\n\n  // Mutations\n  const [updateCartItem] = useMutation(UPDATE_CART_MUTATION, {\n    onError: error => {\n      console.error('Update cart error:', error);\n    },\n    errorPolicy: 'all'\n  });\n\n  // Prevent rendering if the popup is closed\n  if (!isOpen) return null;\n\n  // Show loading state\n  if (loading) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cart-popup\",\n    children: \"Loading...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 35,\n    columnNumber: 23\n  }, this);\n\n  // Show error state\n  if (error) return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"cart-popup\",\n    children: [\"Error loading cart: \", error.message]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 21\n  }, this);\n\n  // Use data from query instead of props for consistency\n  const actualCartItems = (data === null || data === void 0 ? void 0 : data.cart) || cartItems || [];\n  const calculateTotal = () => {\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return '0.00';\n    return actualCartItems.reduce((total, item) => total + item.product.price * item.quantity, 0).toFixed(2);\n  };\n  const getTotalItemCount = () => {\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return 0;\n    return actualCartItems.reduce((total, item) => total + item.quantity, 0);\n  };\n\n  // Helper function to convert attribute name to kebab case\n  const toKebabCase = str => {\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n  };\n\n  // Note: Size and color selection are non-functional in cart as per requirements\n  // Attributes are display-only\n\n  // Get selected options for an item from cart data\n  const getSelectedOptions = item => {\n    try {\n      if (item.selectedAttributes) {\n        return JSON.parse(item.selectedAttributes);\n      }\n      return {};\n    } catch (error) {\n      console.error('Error parsing selected attributes:', error);\n      return {};\n    }\n  };\n\n  // Parse product attributes\n  const parseProductAttributes = attributesString => {\n    try {\n      if (!attributesString) return {};\n      return JSON.parse(attributesString);\n    } catch (error) {\n      return {};\n    }\n  };\n\n  // Get attribute options for display\n  const getAttributeOptions = (attributes, attributeName) => {\n    const parsedAttributes = parseProductAttributes(attributes);\n    return parsedAttributes[attributeName] || [];\n  };\n\n  // Increase Quantity Handler\n  const handleIncreaseQuantity = itemId => {\n    updateCartItem({\n      variables: {\n        itemId,\n        quantityChange: 1\n      },\n      refetchQueries: [{\n        query: GET_CART_QUERY\n      }]\n    }).catch(error => {\n      console.error('Error increasing quantity:', error);\n      showError('Failed to update quantity. Please try again.');\n    });\n  };\n\n  // Decrease Quantity Handler - now removes item when quantity reaches 0\n  const handleDecreaseQuantity = (itemId, currentQuantity) => {\n    if (currentQuantity > 1) {\n      // Decrease quantity by 1\n      updateCartItem({\n        variables: {\n          itemId,\n          quantityChange: -1\n        },\n        refetchQueries: [{\n          query: GET_CART_QUERY\n        }]\n      }).catch(error => {\n        console.error('Error decreasing quantity:', error);\n        alert('Failed to update quantity. Please try again.');\n      });\n    } else {\n      // When quantity is 1, decrease it to 0 which should remove the item\n      updateCartItem({\n        variables: {\n          itemId,\n          quantityChange: -1\n        },\n        refetchQueries: [{\n          query: GET_CART_QUERY\n        }],\n        awaitRefetchQueries: true\n      }).catch(error => {\n        console.error('Error removing item via quantity decrease:', error);\n        alert('Failed to remove item. Please try again.');\n      });\n    }\n  };\n\n  // Place Order Handler using dedicated endpoint\n  const handlePlaceOrder = async () => {\n    try {\n      var _result$data, _result$data$placeOrd;\n      const backendUrl = process.env.REACT_APP_BACKEND_URL || 'https://glidel.store';\n      const response = await fetch(`${backendUrl}/place_order_endpoint.php`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          query: `\n            mutation PlaceOrder {\n              placeOrder {\n                success\n                message\n              }\n            }\n          `\n        })\n      });\n      const result = await response.json();\n      if ((_result$data = result.data) !== null && _result$data !== void 0 && (_result$data$placeOrd = _result$data.placeOrder) !== null && _result$data$placeOrd !== void 0 && _result$data$placeOrd.success) {\n        // Refetch the cart data to update UI\n        await refetch();\n        showSuccess('Order placed successfully!');\n        closePopup(); // Close the cart popup\n      } else {\n        var _result$data2, _result$data2$placeOr;\n        throw new Error(((_result$data2 = result.data) === null || _result$data2 === void 0 ? void 0 : (_result$data2$placeOr = _result$data2.placeOrder) === null || _result$data2$placeOr === void 0 ? void 0 : _result$data2$placeOr.message) || 'Order failed');\n      }\n    } catch (err) {\n      console.error('Place order error:', err);\n      showError(`Failed to place order: ${err.message}`);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"modalBackground\",\n    onClick: closePopup,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"modalContainer\",\n      onClick: e => e.stopPropagation(),\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: [\"My Bag, \", getTotalItemCount(), \" items\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"close-btn\",\n          onClick: closePopup,\n          children: \"\\xD7\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), !actualCartItems || actualCartItems.length === 0 ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Your cart is empty.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 176,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cart-items-container\",\n          children: actualCartItems === null || actualCartItems === void 0 ? void 0 : actualCartItems.map((item, index) => {\n            const itemOptions = getSelectedOptions(item);\n\n            // Get available sizes and colors from product attributes\n            const availableSizes = getAttributeOptions(item.product.attributes, 'Size') || getAttributeOptions(item.product.attributes, 'size') || ['XS', 'S', 'M', 'L']; // fallback\n            const availableColors = getAttributeOptions(item.product.attributes, 'Color') || getAttributeOptions(item.product.attributes, 'color') || ['#C4D79B', '#2B5D31', '#0F4C3A']; // fallback\n\n            return /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"cart-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-item-left\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"product-name\",\n                  children: item.product.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"product-price\",\n                  \"data-testid\": \"cart-item-amount\",\n                  children: [\"$\", item.product.price]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 23\n                }, this), (availableSizes.length > 0 || itemOptions.Size || itemOptions.size) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"size-section\",\n                  \"data-testid\": `cart-item-attribute-${toKebabCase('Size')}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"size-label\",\n                    children: \"Size:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 201,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"size-options\",\n                    children: availableSizes.map(size => /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `size-btn ${itemOptions.Size === size || itemOptions.size === size ? 'selected' : ''} non-clickable`,\n                      \"data-testid\": `cart-item-attribute-${toKebabCase('Size')}-${toKebabCase(size)}${itemOptions.Size === size || itemOptions.size === size ? '-selected' : ''}`,\n                      children: size\n                    }, size, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 204,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 202,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 25\n                }, this), (availableColors.length > 0 || itemOptions.Color || itemOptions.color) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"color-section\",\n                  \"data-testid\": `cart-item-attribute-${toKebabCase('Color')}`,\n                  children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"color-label\",\n                    children: \"Color:\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 218,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"color-options\",\n                    children: availableColors.map(color => /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `color-circle ${itemOptions.Color === color || itemOptions.color === color ? 'selected' : ''} non-clickable`,\n                      style: {\n                        backgroundColor: color\n                      },\n                      \"data-testid\": `cart-item-attribute-${toKebabCase('Color')}-${toKebabCase(color)}${itemOptions.Color === color || itemOptions.color === color ? '-selected' : ''}`\n                    }, color, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 221,\n                      columnNumber: 31\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 219,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"cart-item-right\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-quantity-container\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"cart-item-controls\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"quantity-btn quantity-btn-increase\",\n                      \"data-testid\": \"cart-item-amount-increase\",\n                      onClick: () => handleIncreaseQuantity(item.id),\n                      children: \"+\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 236,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"quantity-display\",\n                      children: item.quantity\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 243,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"quantity-btn quantity-btn-decrease\",\n                      \"data-testid\": \"cart-item-amount-decrease\",\n                      onClick: () => handleDecreaseQuantity(item.id, item.quantity),\n                      children: \"\\u2013\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 244,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"cart-item-image\",\n                    children: /*#__PURE__*/_jsxDEV(\"img\", {\n                      src: item.product.image,\n                      alt: item.product.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 253,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 252,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"item-number\",\n                  children: index + 1\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 19\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"total-section\",\n          \"data-testid\": \"cart-total\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"Total\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: [\"$\", calculateTotal()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handlePlaceOrder,\n          className: \"place-order-btn\",\n          children: \"Place Order\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 267,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n}\n_s(CartPopup, \"Y0V6Kb6pjlCnECE4u+csMsnxZd8=\", false, function () {\n  return [useQuery, useToast, useMutation];\n});\n_c = CartPopup;\nexport default CartPopup;\nvar _c;\n$RefreshReg$(_c, \"CartPopup\");", "map": {"version": 3, "names": ["useState", "useQuery", "useMutation", "GET_CART_QUERY", "UPDATE_CART_MUTATION", "useToast", "jsxDEV", "_jsxDEV", "CartPopup", "isOpen", "closePopup", "cartItems", "_s", "loading", "error", "data", "refetch", "fetchPolicy", "notifyOnNetworkStatusChange", "selectedOptions", "showSuccess", "showError", "updateCartItem", "onError", "console", "errorPolicy", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "message", "actualCartItems", "cart", "calculateTotal", "Array", "isArray", "reduce", "total", "item", "product", "price", "quantity", "toFixed", "getTotalItemCount", "toKebabCase", "str", "toLowerCase", "replace", "getSelectedOptions", "selectedAttributes", "JSON", "parse", "parseProductAttributes", "attributesString", "getAttributeOptions", "attributes", "attributeName", "parsedAttributes", "handleIncreaseQuantity", "itemId", "variables", "quantityChange", "refetchQueries", "query", "catch", "handleDecreaseQuantity", "currentQuantity", "alert", "awaitRefetchQueries", "handlePlaceOrder", "_result$data", "_result$data$placeOrd", "backendUrl", "process", "env", "REACT_APP_BACKEND_URL", "response", "fetch", "method", "headers", "body", "stringify", "result", "json", "placeOrder", "success", "_result$data2", "_result$data2$placeOr", "Error", "err", "onClick", "e", "stopPropagation", "length", "map", "index", "itemOptions", "availableSizes", "availableColors", "name", "Size", "size", "Color", "color", "style", "backgroundColor", "id", "src", "image", "alt", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/CartPopup.js"], "sourcesContent": ["import { useState } from 'react';\r\nimport { useQuery, useMutation } from '@apollo/client';\r\nimport { GET_CART_QUERY } from '../graphql/queries';\r\nimport { UPDATE_CART_MUTATION } from '../graphql/mutations';\r\nimport { useToast } from '../contexts/ToastContext';\r\nimport '../styles/CartPopup.css';\r\n\r\nfunction CartPopup({ isOpen, closePopup, cartItems }) {\r\n  const { loading, error, data, refetch } = useQuery(GET_CART_QUERY, {\r\n    fetchPolicy: 'cache-and-network',\r\n    notifyOnNetworkStatusChange: true\r\n  });\r\n\r\n  // State for managing selected options for each cart item\r\n  const [selectedOptions] = useState({});\r\n  const { showSuccess, showError } = useToast();\r\n\r\n\r\n\r\n\r\n  // Mutations\r\n  const [updateCartItem] = useMutation(UPDATE_CART_MUTATION, {\r\n    onError: (error) => {\r\n      console.error('Update cart error:', error);\r\n    },\r\n    errorPolicy: 'all'\r\n  });\r\n\r\n\r\n\r\n  // Prevent rendering if the popup is closed\r\n  if (!isOpen) return null;\r\n\r\n  // Show loading state\r\n  if (loading) return <div className=\"cart-popup\">Loading...</div>;\r\n\r\n  // Show error state\r\n  if (error) return <div className=\"cart-popup\">Error loading cart: {error.message}</div>;\r\n\r\n  // Use data from query instead of props for consistency\r\n  const actualCartItems = data?.cart || cartItems || [];\r\n\r\n  const calculateTotal = () => {\r\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return '0.00';\r\n    return actualCartItems.reduce((total, item) => total + item.product.price * item.quantity, 0).toFixed(2);\r\n  };\r\n\r\n  const getTotalItemCount = () => {\r\n    if (!actualCartItems || !Array.isArray(actualCartItems)) return 0;\r\n    return actualCartItems.reduce((total, item) => total + item.quantity, 0);\r\n  };\r\n\r\n  // Helper function to convert attribute name to kebab case\r\n  const toKebabCase = (str) => {\r\n    return str.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\r\n  };\r\n\r\n  // Note: Size and color selection are non-functional in cart as per requirements\r\n  // Attributes are display-only\r\n\r\n  // Get selected options for an item from cart data\r\n  const getSelectedOptions = (item) => {\r\n    try {\r\n      if (item.selectedAttributes) {\r\n        return JSON.parse(item.selectedAttributes);\r\n      }\r\n      return {};\r\n    } catch (error) {\r\n      console.error('Error parsing selected attributes:', error);\r\n      return {};\r\n    }\r\n  };\r\n\r\n  // Parse product attributes\r\n  const parseProductAttributes = (attributesString) => {\r\n    try {\r\n      if (!attributesString) return {};\r\n      return JSON.parse(attributesString);\r\n    } catch (error) {\r\n      return {};\r\n    }\r\n  };\r\n\r\n  // Get attribute options for display\r\n  const getAttributeOptions = (attributes, attributeName) => {\r\n    const parsedAttributes = parseProductAttributes(attributes);\r\n    return parsedAttributes[attributeName] || [];\r\n  };\r\n\r\n  // Increase Quantity Handler\r\n  const handleIncreaseQuantity = (itemId) => {\r\n    updateCartItem({\r\n      variables: { itemId, quantityChange: 1 },\r\n      refetchQueries: [{ query: GET_CART_QUERY }],\r\n    })\r\n    .catch((error) => {\r\n      console.error('Error increasing quantity:', error);\r\n      showError('Failed to update quantity. Please try again.');\r\n    });\r\n  };\r\n\r\n  // Decrease Quantity Handler - now removes item when quantity reaches 0\r\n  const handleDecreaseQuantity = (itemId, currentQuantity) => {\r\n    if (currentQuantity > 1) {\r\n      // Decrease quantity by 1\r\n      updateCartItem({\r\n        variables: { itemId, quantityChange: -1 },\r\n        refetchQueries: [{ query: GET_CART_QUERY }],\r\n      })\r\n      .catch((error) => {\r\n        console.error('Error decreasing quantity:', error);\r\n        alert('Failed to update quantity. Please try again.');\r\n      });\r\n    } else {\r\n      // When quantity is 1, decrease it to 0 which should remove the item\r\n      updateCartItem({\r\n        variables: { itemId, quantityChange: -1 },\r\n        refetchQueries: [{ query: GET_CART_QUERY }],\r\n        awaitRefetchQueries: true\r\n      })\r\n      .catch((error) => {\r\n        console.error('Error removing item via quantity decrease:', error);\r\n        alert('Failed to remove item. Please try again.');\r\n      });\r\n    }\r\n  };\r\n\r\n\r\n\r\n  // Place Order Handler using dedicated endpoint\r\n  const handlePlaceOrder = async () => {\r\n    try {\r\n      const backendUrl = process.env.REACT_APP_BACKEND_URL || 'https://glidel.store';\r\n      const response = await fetch(`${backendUrl}/place_order_endpoint.php`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          query: `\r\n            mutation PlaceOrder {\r\n              placeOrder {\r\n                success\r\n                message\r\n              }\r\n            }\r\n          `\r\n        })\r\n      });\r\n\r\n      const result = await response.json();\r\n\r\n      if (result.data?.placeOrder?.success) {\r\n        // Refetch the cart data to update UI\r\n        await refetch();\r\n        showSuccess('Order placed successfully!');\r\n        closePopup(); // Close the cart popup\r\n      } else {\r\n        throw new Error(result.data?.placeOrder?.message || 'Order failed');\r\n      }\r\n\r\n    } catch (err) {\r\n      console.error('Place order error:', err);\r\n      showError(`Failed to place order: ${err.message}`);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"modalBackground\" onClick={closePopup}>\r\n      <div className=\"modalContainer\" onClick={(e) => e.stopPropagation()}>\r\n        <div className=\"cart-header\">\r\n          <h2>My Bag, {getTotalItemCount()} items</h2>\r\n          <button className=\"close-btn\" onClick={closePopup}>×</button>\r\n        </div>\r\n        {!actualCartItems || actualCartItems.length === 0 ? (\r\n          <p>Your cart is empty.</p>\r\n        ) : (\r\n          <div className=\"cart-content\">\r\n            <div className=\"cart-items-container\">\r\n              {actualCartItems?.map((item, index) => {\r\n                const itemOptions = getSelectedOptions(item);\r\n\r\n                // Get available sizes and colors from product attributes\r\n                const availableSizes = getAttributeOptions(item.product.attributes, 'Size') ||\r\n                                     getAttributeOptions(item.product.attributes, 'size') ||\r\n                                     ['XS', 'S', 'M', 'L']; // fallback\r\n                const availableColors = getAttributeOptions(item.product.attributes, 'Color') ||\r\n                                       getAttributeOptions(item.product.attributes, 'color') ||\r\n                                       ['#C4D79B', '#2B5D31', '#0F4C3A']; // fallback\r\n\r\n\r\n\r\n                return (\r\n                  <div key={item.id} className=\"cart-item\">\r\n                    <div className=\"cart-item-left\">\r\n                      <h3 className=\"product-name\">{item.product.name}</h3>\r\n                      <p className=\"product-price\" data-testid='cart-item-amount'>${item.product.price}</p>\r\n\r\n                      {(availableSizes.length > 0 || itemOptions.Size || itemOptions.size) && (\r\n                        <div className=\"size-section\" data-testid={`cart-item-attribute-${toKebabCase('Size')}`}>\r\n                          <span className=\"size-label\">Size:</span>\r\n                          <div className=\"size-options\">\r\n                            {availableSizes.map(size => (\r\n                              <span\r\n                                key={size}\r\n                                className={`size-btn ${(itemOptions.Size === size || itemOptions.size === size) ? 'selected' : ''} non-clickable`}\r\n                                data-testid={`cart-item-attribute-${toKebabCase('Size')}-${toKebabCase(size)}${(itemOptions.Size === size || itemOptions.size === size) ? '-selected' : ''}`}\r\n                              >\r\n                                {size}\r\n                              </span>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n\r\n                      {(availableColors.length > 0 || itemOptions.Color || itemOptions.color) && (\r\n                        <div className=\"color-section\" data-testid={`cart-item-attribute-${toKebabCase('Color')}`}>\r\n                          <span className=\"color-label\">Color:</span>\r\n                          <div className=\"color-options\">\r\n                            {availableColors.map(color => (\r\n                              <div\r\n                                key={color}\r\n                                className={`color-circle ${(itemOptions.Color === color || itemOptions.color === color) ? 'selected' : ''} non-clickable`}\r\n                                style={{backgroundColor: color}}\r\n                                data-testid={`cart-item-attribute-${toKebabCase('Color')}-${toKebabCase(color)}${(itemOptions.Color === color || itemOptions.color === color) ? '-selected' : ''}`}\r\n                              ></div>\r\n                            ))}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n\r\n                  <div className=\"cart-item-right\">\r\n                    <div className=\"image-quantity-container\">\r\n                      <div className=\"cart-item-controls\">\r\n                        <button\r\n                          className=\"quantity-btn quantity-btn-increase\"\r\n                          data-testid='cart-item-amount-increase'\r\n                          onClick={() => handleIncreaseQuantity(item.id)}\r\n                        >\r\n                          +\r\n                        </button>\r\n                        <span className=\"quantity-display\">{item.quantity}</span>\r\n                        <button\r\n                          className=\"quantity-btn quantity-btn-decrease\"\r\n                          data-testid='cart-item-amount-decrease'\r\n                          onClick={() => handleDecreaseQuantity(item.id, item.quantity)}\r\n                        >\r\n                          –\r\n                        </button>\r\n                      </div>\r\n                      <div className=\"cart-item-image\">\r\n                        <img src={item.product.image} alt={item.product.name} />\r\n                      </div>\r\n                    </div>\r\n                    <div className=\"item-number\">{index + 1}</div>\r\n                  </div>\r\n                </div>\r\n                );\r\n              })}\r\n            </div>\r\n            {/* Total Section */}\r\n            <div className=\"total-section\" data-testid=\"cart-total\">\r\n              <p>Total</p>\r\n              <p>${calculateTotal()}</p>\r\n            </div>\r\n            <button onClick={handlePlaceOrder} className=\"place-order-btn\">\r\n              Place Order\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default CartPopup;\r\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,QAAQ,EAAEC,WAAW,QAAQ,gBAAgB;AACtD,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,oBAAoB,QAAQ,sBAAsB;AAC3D,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,OAAO,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjC,SAASC,SAASA,CAAC;EAAEC,MAAM;EAAEC,UAAU;EAAEC;AAAU,CAAC,EAAE;EAAAC,EAAA;EACpD,MAAM;IAAEC,OAAO;IAAEC,KAAK;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGf,QAAQ,CAACE,cAAc,EAAE;IACjEc,WAAW,EAAE,mBAAmB;IAChCC,2BAA2B,EAAE;EAC/B,CAAC,CAAC;;EAEF;EACA,MAAM,CAACC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACtC,MAAM;IAAEoB,WAAW;IAAEC;EAAU,CAAC,GAAGhB,QAAQ,CAAC,CAAC;;EAK7C;EACA,MAAM,CAACiB,cAAc,CAAC,GAAGpB,WAAW,CAACE,oBAAoB,EAAE;IACzDmB,OAAO,EAAGT,KAAK,IAAK;MAClBU,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;IAC5C,CAAC;IACDW,WAAW,EAAE;EACf,CAAC,CAAC;;EAIF;EACA,IAAI,CAAChB,MAAM,EAAE,OAAO,IAAI;;EAExB;EACA,IAAII,OAAO,EAAE,oBAAON,OAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAC,QAAA,EAAC;EAAU;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;;EAEhE;EACA,IAAIjB,KAAK,EAAE,oBAAOP,OAAA;IAAKmB,SAAS,EAAC,YAAY;IAAAC,QAAA,GAAC,sBAAoB,EAACb,KAAK,CAACkB,OAAO;EAAA;IAAAJ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAM,CAAC;;EAEvF;EACA,MAAME,eAAe,GAAG,CAAAlB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmB,IAAI,KAAIvB,SAAS,IAAI,EAAE;EAErD,MAAMwB,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAACF,eAAe,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE,OAAO,MAAM;IACtE,OAAOA,eAAe,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,OAAO,CAACC,KAAK,GAAGF,IAAI,CAACG,QAAQ,EAAE,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;EAC1G,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI,CAACZ,eAAe,IAAI,CAACG,KAAK,CAACC,OAAO,CAACJ,eAAe,CAAC,EAAE,OAAO,CAAC;IACjE,OAAOA,eAAe,CAACK,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACG,QAAQ,EAAE,CAAC,CAAC;EAC1E,CAAC;;EAED;EACA,MAAMG,WAAW,GAAIC,GAAG,IAAK;IAC3B,OAAOA,GAAG,CAACC,WAAW,CAAC,CAAC,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;EAC1E,CAAC;;EAED;EACA;;EAEA;EACA,MAAMC,kBAAkB,GAAIV,IAAI,IAAK;IACnC,IAAI;MACF,IAAIA,IAAI,CAACW,kBAAkB,EAAE;QAC3B,OAAOC,IAAI,CAACC,KAAK,CAACb,IAAI,CAACW,kBAAkB,CAAC;MAC5C;MACA,OAAO,CAAC,CAAC;IACX,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC1D,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAMwC,sBAAsB,GAAIC,gBAAgB,IAAK;IACnD,IAAI;MACF,IAAI,CAACA,gBAAgB,EAAE,OAAO,CAAC,CAAC;MAChC,OAAOH,IAAI,CAACC,KAAK,CAACE,gBAAgB,CAAC;IACrC,CAAC,CAAC,OAAOzC,KAAK,EAAE;MACd,OAAO,CAAC,CAAC;IACX;EACF,CAAC;;EAED;EACA,MAAM0C,mBAAmB,GAAGA,CAACC,UAAU,EAAEC,aAAa,KAAK;IACzD,MAAMC,gBAAgB,GAAGL,sBAAsB,CAACG,UAAU,CAAC;IAC3D,OAAOE,gBAAgB,CAACD,aAAa,CAAC,IAAI,EAAE;EAC9C,CAAC;;EAED;EACA,MAAME,sBAAsB,GAAIC,MAAM,IAAK;IACzCvC,cAAc,CAAC;MACbwC,SAAS,EAAE;QAAED,MAAM;QAAEE,cAAc,EAAE;MAAE,CAAC;MACxCC,cAAc,EAAE,CAAC;QAAEC,KAAK,EAAE9D;MAAe,CAAC;IAC5C,CAAC,CAAC,CACD+D,KAAK,CAAEpD,KAAK,IAAK;MAChBU,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDO,SAAS,CAAC,8CAA8C,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM8C,sBAAsB,GAAGA,CAACN,MAAM,EAAEO,eAAe,KAAK;IAC1D,IAAIA,eAAe,GAAG,CAAC,EAAE;MACvB;MACA9C,cAAc,CAAC;QACbwC,SAAS,EAAE;UAAED,MAAM;UAAEE,cAAc,EAAE,CAAC;QAAE,CAAC;QACzCC,cAAc,EAAE,CAAC;UAAEC,KAAK,EAAE9D;QAAe,CAAC;MAC5C,CAAC,CAAC,CACD+D,KAAK,CAAEpD,KAAK,IAAK;QAChBU,OAAO,CAACV,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDuD,KAAK,CAAC,8CAA8C,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA/C,cAAc,CAAC;QACbwC,SAAS,EAAE;UAAED,MAAM;UAAEE,cAAc,EAAE,CAAC;QAAE,CAAC;QACzCC,cAAc,EAAE,CAAC;UAAEC,KAAK,EAAE9D;QAAe,CAAC,CAAC;QAC3CmE,mBAAmB,EAAE;MACvB,CAAC,CAAC,CACDJ,KAAK,CAAEpD,KAAK,IAAK;QAChBU,OAAO,CAACV,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClEuD,KAAK,CAAC,0CAA0C,CAAC;MACnD,CAAC,CAAC;IACJ;EACF,CAAC;;EAID;EACA,MAAME,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MAAA,IAAAC,YAAA,EAAAC,qBAAA;MACF,MAAMC,UAAU,GAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB,IAAI,sBAAsB;MAC9E,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,UAAU,2BAA2B,EAAE;QACrEM,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAE9B,IAAI,CAAC+B,SAAS,CAAC;UACnBlB,KAAK,EAAE;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;QACQ,CAAC;MACH,CAAC,CAAC;MAEF,MAAMmB,MAAM,GAAG,MAAMN,QAAQ,CAACO,IAAI,CAAC,CAAC;MAEpC,KAAAb,YAAA,GAAIY,MAAM,CAACrE,IAAI,cAAAyD,YAAA,gBAAAC,qBAAA,GAAXD,YAAA,CAAac,UAAU,cAAAb,qBAAA,eAAvBA,qBAAA,CAAyBc,OAAO,EAAE;QACpC;QACA,MAAMvE,OAAO,CAAC,CAAC;QACfI,WAAW,CAAC,4BAA4B,CAAC;QACzCV,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QAAA,IAAA8E,aAAA,EAAAC,qBAAA;QACL,MAAM,IAAIC,KAAK,CAAC,EAAAF,aAAA,GAAAJ,MAAM,CAACrE,IAAI,cAAAyE,aAAA,wBAAAC,qBAAA,GAAXD,aAAA,CAAaF,UAAU,cAAAG,qBAAA,uBAAvBA,qBAAA,CAAyBzD,OAAO,KAAI,cAAc,CAAC;MACrE;IAEF,CAAC,CAAC,OAAO2D,GAAG,EAAE;MACZnE,OAAO,CAACV,KAAK,CAAC,oBAAoB,EAAE6E,GAAG,CAAC;MACxCtE,SAAS,CAAC,0BAA0BsE,GAAG,CAAC3D,OAAO,EAAE,CAAC;IACpD;EACF,CAAC;EAED,oBACEzB,OAAA;IAAKmB,SAAS,EAAC,iBAAiB;IAACkE,OAAO,EAAElF,UAAW;IAAAiB,QAAA,eACnDpB,OAAA;MAAKmB,SAAS,EAAC,gBAAgB;MAACkE,OAAO,EAAGC,CAAC,IAAKA,CAAC,CAACC,eAAe,CAAC,CAAE;MAAAnE,QAAA,gBAClEpB,OAAA;QAAKmB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BpB,OAAA;UAAAoB,QAAA,GAAI,UAAQ,EAACkB,iBAAiB,CAAC,CAAC,EAAC,QAAM;QAAA;UAAAjB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CxB,OAAA;UAAQmB,SAAS,EAAC,WAAW;UAACkE,OAAO,EAAElF,UAAW;UAAAiB,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1D,CAAC,EACL,CAACE,eAAe,IAAIA,eAAe,CAAC8D,MAAM,KAAK,CAAC,gBAC/CxF,OAAA;QAAAoB,QAAA,EAAG;MAAmB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBAE1BxB,OAAA;QAAKmB,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BpB,OAAA;UAAKmB,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAClCM,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAE+D,GAAG,CAAC,CAACxD,IAAI,EAAEyD,KAAK,KAAK;YACrC,MAAMC,WAAW,GAAGhD,kBAAkB,CAACV,IAAI,CAAC;;YAE5C;YACA,MAAM2D,cAAc,GAAG3C,mBAAmB,CAAChB,IAAI,CAACC,OAAO,CAACgB,UAAU,EAAE,MAAM,CAAC,IACtDD,mBAAmB,CAAChB,IAAI,CAACC,OAAO,CAACgB,UAAU,EAAE,MAAM,CAAC,IACpD,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5C,MAAM2C,eAAe,GAAG5C,mBAAmB,CAAChB,IAAI,CAACC,OAAO,CAACgB,UAAU,EAAE,OAAO,CAAC,IACtDD,mBAAmB,CAAChB,IAAI,CAACC,OAAO,CAACgB,UAAU,EAAE,OAAO,CAAC,IACrD,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;;YAI1D,oBACElD,OAAA;cAAmBmB,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACtCpB,OAAA;gBAAKmB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BpB,OAAA;kBAAImB,SAAS,EAAC,cAAc;kBAAAC,QAAA,EAAEa,IAAI,CAACC,OAAO,CAAC4D;gBAAI;kBAAAzE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACrDxB,OAAA;kBAAGmB,SAAS,EAAC,eAAe;kBAAC,eAAY,kBAAkB;kBAAAC,QAAA,GAAC,GAAC,EAACa,IAAI,CAACC,OAAO,CAACC,KAAK;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,EAEpF,CAACoE,cAAc,CAACJ,MAAM,GAAG,CAAC,IAAIG,WAAW,CAACI,IAAI,IAAIJ,WAAW,CAACK,IAAI,kBACjEhG,OAAA;kBAAKmB,SAAS,EAAC,cAAc;kBAAC,eAAa,uBAAuBoB,WAAW,CAAC,MAAM,CAAC,EAAG;kBAAAnB,QAAA,gBACtFpB,OAAA;oBAAMmB,SAAS,EAAC,YAAY;oBAAAC,QAAA,EAAC;kBAAK;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCxB,OAAA;oBAAKmB,SAAS,EAAC,cAAc;oBAAAC,QAAA,EAC1BwE,cAAc,CAACH,GAAG,CAACO,IAAI,iBACtBhG,OAAA;sBAEEmB,SAAS,EAAE,YAAawE,WAAW,CAACI,IAAI,KAAKC,IAAI,IAAIL,WAAW,CAACK,IAAI,KAAKA,IAAI,GAAI,UAAU,GAAG,EAAE,gBAAiB;sBAClH,eAAa,uBAAuBzD,WAAW,CAAC,MAAM,CAAC,IAAIA,WAAW,CAACyD,IAAI,CAAC,GAAIL,WAAW,CAACI,IAAI,KAAKC,IAAI,IAAIL,WAAW,CAACK,IAAI,KAAKA,IAAI,GAAI,WAAW,GAAG,EAAE,EAAG;sBAAA5E,QAAA,EAE5J4E;oBAAI,GAJAA,IAAI;sBAAA3E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAKL,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAEA,CAACqE,eAAe,CAACL,MAAM,GAAG,CAAC,IAAIG,WAAW,CAACM,KAAK,IAAIN,WAAW,CAACO,KAAK,kBACpElG,OAAA;kBAAKmB,SAAS,EAAC,eAAe;kBAAC,eAAa,uBAAuBoB,WAAW,CAAC,OAAO,CAAC,EAAG;kBAAAnB,QAAA,gBACxFpB,OAAA;oBAAMmB,SAAS,EAAC,aAAa;oBAAAC,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC3CxB,OAAA;oBAAKmB,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAC3ByE,eAAe,CAACJ,GAAG,CAACS,KAAK,iBACxBlG,OAAA;sBAEEmB,SAAS,EAAE,gBAAiBwE,WAAW,CAACM,KAAK,KAAKC,KAAK,IAAIP,WAAW,CAACO,KAAK,KAAKA,KAAK,GAAI,UAAU,GAAG,EAAE,gBAAiB;sBAC1HC,KAAK,EAAE;wBAACC,eAAe,EAAEF;sBAAK,CAAE;sBAChC,eAAa,uBAAuB3D,WAAW,CAAC,OAAO,CAAC,IAAIA,WAAW,CAAC2D,KAAK,CAAC,GAAIP,WAAW,CAACM,KAAK,KAAKC,KAAK,IAAIP,WAAW,CAACO,KAAK,KAAKA,KAAK,GAAI,WAAW,GAAG,EAAE;oBAAG,GAH9JA,KAAK;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAIN,CACP;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAERxB,OAAA;gBAAKmB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BpB,OAAA;kBAAKmB,SAAS,EAAC,0BAA0B;kBAAAC,QAAA,gBACvCpB,OAAA;oBAAKmB,SAAS,EAAC,oBAAoB;oBAAAC,QAAA,gBACjCpB,OAAA;sBACEmB,SAAS,EAAC,oCAAoC;sBAC9C,eAAY,2BAA2B;sBACvCkE,OAAO,EAAEA,CAAA,KAAMhC,sBAAsB,CAACpB,IAAI,CAACoE,EAAE,CAAE;sBAAAjF,QAAA,EAChD;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eACTxB,OAAA;sBAAMmB,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,EAAEa,IAAI,CAACG;oBAAQ;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACzDxB,OAAA;sBACEmB,SAAS,EAAC,oCAAoC;sBAC9C,eAAY,2BAA2B;sBACvCkE,OAAO,EAAEA,CAAA,KAAMzB,sBAAsB,CAAC3B,IAAI,CAACoE,EAAE,EAAEpE,IAAI,CAACG,QAAQ,CAAE;sBAAAhB,QAAA,EAC/D;oBAED;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eACNxB,OAAA;oBAAKmB,SAAS,EAAC,iBAAiB;oBAAAC,QAAA,eAC9BpB,OAAA;sBAAKsG,GAAG,EAAErE,IAAI,CAACC,OAAO,CAACqE,KAAM;sBAACC,GAAG,EAAEvE,IAAI,CAACC,OAAO,CAAC4D;oBAAK;sBAAAzE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNxB,OAAA;kBAAKmB,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAEsE,KAAK,GAAG;gBAAC;kBAAArE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC;YAAA,GA/DIS,IAAI,CAACoE,EAAE;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAgEd,CAAC;UAER,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENxB,OAAA;UAAKmB,SAAS,EAAC,eAAe;UAAC,eAAY,YAAY;UAAAC,QAAA,gBACrDpB,OAAA;YAAAoB,QAAA,EAAG;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACZxB,OAAA;YAAAoB,QAAA,GAAG,GAAC,EAACQ,cAAc,CAAC,CAAC;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACNxB,OAAA;UAAQqF,OAAO,EAAErB,gBAAiB;UAAC7C,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAE/D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACnB,EAAA,CA3QQJ,SAAS;EAAA,QAC0BP,QAAQ,EAOfI,QAAQ,EAMlBH,WAAW;AAAA;AAAA8G,EAAA,GAd7BxG,SAAS;AA6QlB,eAAeA,SAAS;AAAC,IAAAwG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}