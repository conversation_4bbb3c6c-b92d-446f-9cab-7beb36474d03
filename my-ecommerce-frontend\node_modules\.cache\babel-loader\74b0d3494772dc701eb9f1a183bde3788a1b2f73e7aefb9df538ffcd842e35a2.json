{"ast": null, "code": "var _jsxFileName = \"D:\\\\SCAAND_PRO\\\\my-ecommerce-frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\n// src/components/Navbar.js\nimport { useState, useEffect, useRef } from 'react';\nimport CartPopup from './CartPopup';\nimport { useQuery } from '@apollo/client';\nimport { GET_CART_QUERY } from '../graphql/queries';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport logo from '../assets/img/Group.svg';\nimport cart from '../assets/img/Vector.svg';\nimport '../styles/NavBar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Navbar() {\n  _s();\n  var _data$cart;\n  const [isCartOpen, setIsCartOpen] = useState(false);\n  const {\n    data\n  } = useQuery(GET_CART_QUERY);\n  const location = useLocation();\n  const cartRef = useRef(null);\n  const cartCount = (data === null || data === void 0 ? void 0 : (_data$cart = data.cart) === null || _data$cart === void 0 ? void 0 : _data$cart.reduce((total, item) => total + item.quantity, 0)) || 0;\n\n  // Close cart when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (cartRef.current && !cartRef.current.contains(event.target) && isCartOpen) {\n        setIsCartOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, [isCartOpen]);\n\n  // Close cart when switching categories/routes\n  useEffect(() => {\n    setIsCartOpen(false);\n  }, [location.pathname]);\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-left\",\n      children: [/*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/\",\n        className: ({\n          isActive\n        }) => isActive ? 'active' : '',\n        \"data-testid\": ({\n          isActive\n        }) => isActive ? 'active-category-link' : 'category-link',\n        children: \"All\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/category/clothes\",\n        className: ({\n          isActive\n        }) => isActive ? 'active' : '',\n        \"data-testid\": ({\n          isActive\n        }) => isActive ? 'active-category-link' : 'category-link',\n        children: \"Clothes\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(NavLink, {\n        to: \"/category/tech\",\n        className: ({\n          isActive\n        }) => isActive ? 'active' : '',\n        \"data-testid\": ({\n          isActive\n        }) => isActive ? 'active-category-link' : 'category-link',\n        children: \"Tech\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      src: logo,\n      width: 30,\n      height: 30,\n      alt: \"Logo\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-right\",\n      ref: cartRef,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cart-icon-container\",\n        \"data-testid\": \"cart-btn\",\n        onClick: () => setIsCartOpen(!isCartOpen),\n        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n          src: cart,\n          width: 20,\n          height: 20,\n          alt: \"Cart\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this), cartCount === 0 ? null : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"cart-count\",\n          children: cartCount\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CartPopup, {\n        isOpen: isCartOpen,\n        closePopup: () => setIsCartOpen(false),\n        cartItems: (data === null || data === void 0 ? void 0 : data.cart) || []\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n}\n_s(Navbar, \"SvQBiBPGJFvmW/KwZCLBSIbNFDw=\", false, function () {\n  return [useQuery, useLocation];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["useState", "useEffect", "useRef", "CartPopup", "useQuery", "GET_CART_QUERY", "NavLink", "useLocation", "logo", "cart", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "_s", "_data$cart", "isCartOpen", "setIsCartOpen", "data", "location", "cartRef", "cartCount", "reduce", "total", "item", "quantity", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "pathname", "className", "children", "to", "isActive", "data-testid", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "width", "height", "alt", "ref", "onClick", "isOpen", "closePopup", "cartItems", "_c", "$RefreshReg$"], "sources": ["D:/SCAAND_PRO/my-ecommerce-frontend/src/components/Navbar.js"], "sourcesContent": ["// src/components/Navbar.js\r\nimport { useState, useEffect, useRef } from 'react';\r\nimport CartPopup from './CartPopup';\r\nimport { useQuery } from '@apollo/client';\r\nimport { GET_CART_QUERY } from '../graphql/queries';\r\nimport { NavLink, useLocation } from 'react-router-dom';\r\nimport logo from '../assets/img/Group.svg';\r\nimport cart from '../assets/img/Vector.svg';\r\nimport '../styles/NavBar.css';\r\n\r\nfunction Navbar() {\r\n  const [isCartOpen, setIsCartOpen] = useState(false);\r\n  const { data } = useQuery(GET_CART_QUERY);\r\n  const location = useLocation();\r\n  const cartRef = useRef(null);\r\n\r\n  const cartCount = data?.cart?.reduce((total, item) => total + item.quantity, 0) || 0;\r\n\r\n  // Close cart when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event) => {\r\n      if (cartRef.current && !cartRef.current.contains(event.target) && isCartOpen) {\r\n        setIsCartOpen(false);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, [isCartOpen]);\r\n\r\n  // Close cart when switching categories/routes\r\n  useEffect(() => {\r\n    setIsCartOpen(false);\r\n  }, [location.pathname]);\r\n\r\n  return (\r\n    <nav className=\"navbar\">\r\n      <div className=\"navbar-left\">\r\n        <NavLink to=\"/\" className={({ isActive }) => (isActive ? 'active' : '')} data-testid={({ isActive }) => (isActive ? 'active-category-link' : 'category-link')}>All</NavLink>\r\n        <NavLink to=\"/category/clothes\" className={({ isActive }) => (isActive ? 'active' : '')} data-testid={({ isActive }) => (isActive ? 'active-category-link' : 'category-link')}>Clothes</NavLink>\r\n        <NavLink to=\"/category/tech\" className={({ isActive }) => (isActive ? 'active' : '')} data-testid={({ isActive }) => (isActive ? 'active-category-link' : 'category-link')}>Tech</NavLink>\r\n      </div>\r\n      <img src={logo} width={30} height={30} alt=\"Logo\"/>\r\n      <div className=\"navbar-right\" ref={cartRef}>\r\n        <div className=\"cart-icon-container\" data-testid=\"cart-btn\" onClick={() => setIsCartOpen(!isCartOpen)}>\r\n          <img src={cart} width={20} height={20} alt=\"Cart\"/>\r\n          {cartCount === 0 ? null : (\r\n            <span className=\"cart-count\">{cartCount}</span>\r\n          )}\r\n        </div>\r\n        <CartPopup\r\n          isOpen={isCartOpen}\r\n          closePopup={() => setIsCartOpen(false)}\r\n          cartItems={data?.cart || []}\r\n        />\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n\r\nexport default Navbar;\r\n"], "mappings": ";;AAAA;AACA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,SAAS,MAAM,aAAa;AACnC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,SAASC,cAAc,QAAQ,oBAAoB;AACnD,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,OAAOC,IAAI,MAAM,yBAAyB;AAC1C,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAO,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAAA,IAAAC,UAAA;EAChB,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IAAEiB;EAAK,CAAC,GAAGb,QAAQ,CAACC,cAAc,CAAC;EACzC,MAAMa,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,OAAO,GAAGjB,MAAM,CAAC,IAAI,CAAC;EAE5B,MAAMkB,SAAS,GAAG,CAAAH,IAAI,aAAJA,IAAI,wBAAAH,UAAA,GAAJG,IAAI,CAAER,IAAI,cAAAK,UAAA,uBAAVA,UAAA,CAAYO,MAAM,CAAC,CAACC,KAAK,EAAEC,IAAI,KAAKD,KAAK,GAAGC,IAAI,CAACC,QAAQ,EAAE,CAAC,CAAC,KAAI,CAAC;;EAEpF;EACAvB,SAAS,CAAC,MAAM;IACd,MAAMwB,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIP,OAAO,CAACQ,OAAO,IAAI,CAACR,OAAO,CAACQ,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,IAAId,UAAU,EAAE;QAC5EC,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC;IAEDc,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,CAACV,UAAU,CAAC,CAAC;;EAEhB;EACAd,SAAS,CAAC,MAAM;IACde,aAAa,CAAC,KAAK,CAAC;EACtB,CAAC,EAAE,CAACE,QAAQ,CAACe,QAAQ,CAAC,CAAC;EAEvB,oBACEtB,OAAA;IAAKuB,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBxB,OAAA;MAAKuB,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1BxB,OAAA,CAACL,OAAO;QAAC8B,EAAE,EAAC,GAAG;QAACF,SAAS,EAAEA,CAAC;UAAEG;QAAS,CAAC,KAAMA,QAAQ,GAAG,QAAQ,GAAG,EAAI;QAAC,eAAaC,CAAC;UAAED;QAAS,CAAC,KAAMA,QAAQ,GAAG,sBAAsB,GAAG,eAAiB;QAAAF,QAAA,EAAC;MAAG;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAC5K/B,OAAA,CAACL,OAAO;QAAC8B,EAAE,EAAC,mBAAmB;QAACF,SAAS,EAAEA,CAAC;UAAEG;QAAS,CAAC,KAAMA,QAAQ,GAAG,QAAQ,GAAG,EAAI;QAAC,eAAaC,CAAC;UAAED;QAAS,CAAC,KAAMA,QAAQ,GAAG,sBAAsB,GAAG,eAAiB;QAAAF,QAAA,EAAC;MAAO;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,eAChM/B,OAAA,CAACL,OAAO;QAAC8B,EAAE,EAAC,gBAAgB;QAACF,SAAS,EAAEA,CAAC;UAAEG;QAAS,CAAC,KAAMA,QAAQ,GAAG,QAAQ,GAAG,EAAI;QAAC,eAAaC,CAAC;UAAED;QAAS,CAAC,KAAMA,QAAQ,GAAG,sBAAsB,GAAG,eAAiB;QAAAF,QAAA,EAAC;MAAI;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvL,CAAC,eACN/B,OAAA;MAAKgC,GAAG,EAAEnC,IAAK;MAACoC,KAAK,EAAE,EAAG;MAACC,MAAM,EAAE,EAAG;MAACC,GAAG,EAAC;IAAM;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAC,CAAC,eACnD/B,OAAA;MAAKuB,SAAS,EAAC,cAAc;MAACa,GAAG,EAAE5B,OAAQ;MAAAgB,QAAA,gBACzCxB,OAAA;QAAKuB,SAAS,EAAC,qBAAqB;QAAC,eAAY,UAAU;QAACc,OAAO,EAAEA,CAAA,KAAMhC,aAAa,CAAC,CAACD,UAAU,CAAE;QAAAoB,QAAA,gBACpGxB,OAAA;UAAKgC,GAAG,EAAElC,IAAK;UAACmC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACC,GAAG,EAAC;QAAM;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAC,CAAC,EAClDtB,SAAS,KAAK,CAAC,GAAG,IAAI,gBACrBT,OAAA;UAAMuB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEf;QAAS;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAC/C;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eACN/B,OAAA,CAACR,SAAS;QACR8C,MAAM,EAAElC,UAAW;QACnBmC,UAAU,EAAEA,CAAA,KAAMlC,aAAa,CAAC,KAAK,CAAE;QACvCmC,SAAS,EAAE,CAAAlC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAER,IAAI,KAAI;MAAG;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC7B,EAAA,CAlDQD,MAAM;EAAA,QAEIR,QAAQ,EACRG,WAAW;AAAA;AAAA6C,EAAA,GAHrBxC,MAAM;AAoDf,eAAeA,MAAM;AAAC,IAAAwC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}