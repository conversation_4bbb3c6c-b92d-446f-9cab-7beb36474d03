[{"D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\index.js": "1", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\apolloClient.js": "2", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\App.js": "3", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartPopup.js": "4", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductDetails.js": "5", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Navbar.js": "6", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductList.js": "7", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Button.js": "8", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\mutations.js": "9", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\queries.js": "10", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\contexts\\ToastContext.js": "11", "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Toast.js": "12"}, {"size": 325, "mtime": 1752975046932, "results": "13", "hashOfConfig": "14"}, {"size": 1490, "mtime": 1753185786948, "results": "15", "hashOfConfig": "14"}, {"size": 940, "mtime": 1753176886430, "results": "16", "hashOfConfig": "14"}, {"size": 10972, "mtime": 1754844716897, "results": "17", "hashOfConfig": "14"}, {"size": 6874, "mtime": 1754844716945, "results": "18", "hashOfConfig": "14"}, {"size": 1867, "mtime": 1754844716947, "results": "19", "hashOfConfig": "14"}, {"size": 3017, "mtime": 1754844716944, "results": "20", "hashOfConfig": "14"}, {"size": 397, "mtime": 1752974907664, "results": "21", "hashOfConfig": "14"}, {"size": 1603, "mtime": 1754844716946, "results": "22", "hashOfConfig": "14"}, {"size": 766, "mtime": 1754844716947, "results": "23", "hashOfConfig": "14"}, {"size": 1591, "mtime": 1753176868341, "results": "24", "hashOfConfig": "14"}, {"size": 899, "mtime": 1753176817142, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "10vzdib", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\index.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\apolloClient.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\App.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\CartPopup.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductDetails.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Navbar.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\ProductList.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Button.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\mutations.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\graphql\\queries.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\contexts\\ToastContext.js", [], [], "D:\\SCAAND_PRO\\my-ecommerce-frontend\\src\\components\\Toast.js", [], []]