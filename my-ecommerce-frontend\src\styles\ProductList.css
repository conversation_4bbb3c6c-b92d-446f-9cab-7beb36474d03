.products {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
	gap: 30px;
	width: 90%;
	max-width: 1200px;
	margin: 0 auto;
	background: #fff;
	font-family: 'Raleway', sans-serif;
	padding: 20px 0;
}


.product-card {
  position: relative;
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  min-height: 450px;
  border: 1px solid #f0f0f0;
}

.product-card .image-container {
  width: 100%;
  height: 300px;
  overflow: hidden;
  position: relative;
  background-color: #ffffff;
}

.product-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.product-card:hover .product-card__image {
  transform: scale(1.05);
}

.product-card__image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  object-position: center;
  display: block;
  transition: transform 0.3s ease;
}

.product-card__brand {
  margin: 12px 16px 4px 16px;
  font-size: 1rem;
  font-weight: 300;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

.product-card__description {
  font-weight: normal;
}

.product-card__price {
  font-weight: bold;
}

.product-card__btn-wishlist {
  position: absolute;
  top: 10px;
  right: 10px;
  border-radius: 50%;
  height: 40px;
  width: 40px;
  border: none;
  background-color: white;
  padding: 12px 10px 10px;
  box-shadow: 2px 2px 5px 0px rgba(0, 64, 128, 0.1);
}

.product-card__btn-wishlist svg {
  fill: lightgrey;
}

.product-link{
  text-decoration: none;
  color: #1d1f22;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.product-link .product-name {
  margin: 4px 16px 8px 16px;
  font-size: 1rem;
  font-weight: 300;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
  line-height: 1.4;
}

.header {
  grid-column: 1 / -1;
  margin-bottom: 30px;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

.header h1 {
  font-size: 2.5rem;
  font-weight: 400;
  margin: 0;
  text-transform: capitalize;
}

.price {
  margin: 8px 16px 16px 16px;
  font-size: 1.125rem;
  font-weight: 500;
  color: #1d1f22;
  font-family: 'Raleway', sans-serif;
}

/* Add to Cart Button */
.add-to-cart {
  display: block;
  width: 100%;
  padding: 0.75rem;
  font-size: 1rem;
  font-weight: bold;
  color: white;
  background-color: turquoise;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;
  margin: 0;
  position: relative;
  top: -10px;
}

.add-to-cart:hover {
  background-color: rgb(25, 139, 128);
}

/* Quick Shop Button */
.quick-shop-btn {
  position: absolute;
  bottom: 80px;
  right: 16px;
  width: 52px;
  height: 52px;
  border-radius: 50%;
  border: none;
  background-color: #5ECE7B;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 11px rgba(29, 31, 34, 0.1);
  transition: all 0.3s ease;
  opacity: 0;
  visibility: hidden;
  transform: scale(0.8);
  z-index: 10;
}

.quick-shop-btn:hover {
  background-color: #4CAF50;
  transform: scale(1.1);
  box-shadow: 0 6px 15px rgba(29, 31, 34, 0.2);
}

.product-card:hover .quick-shop-btn {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

.quick-shop-btn img {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1); /* Make the icon white */
}